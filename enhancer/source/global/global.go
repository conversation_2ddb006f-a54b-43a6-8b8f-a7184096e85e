package global

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func GetGlobalOrgContext(resourceContext *rcontext.ResourceContext) {

	docID := common.GenerateCombinedHashID(property.ORG_CONTEXT_TYPE, resourceContext.TenantID)

	orgContext, err := elastic.GetDocument(elastic.CUST_ENTITY_CONTEXT_INDEX, docID)
	if err != nil {
		return
	}

	if len(orgContext) > 0 {

		orgContextJson, err := json.Marshal(orgContext)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			return
		}

		var orgEntityContext common.CustomerEntityContextDoc

		if err = json.Unmarshal(orgContextJson, &orgEntityContext); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		for _, contextProperty := range orgEntityContext.ContextProperties {

			for _, includedProperty := range contextProperty.Include {

				switch contextProperty.PropertyName {

				case property.APP_PROPERTY_NAME:

					var appVals = strings.Split(includedProperty, ",")

					appName := appVals[0]

					for _, appVal := range appVals {

						appVal = strings.ToLower(appVal)
						if len(appVal) <= 4 {
							appVal = `\b` + appVal + `\b`
						}
						appVal = strings.ReplaceAll(appVal, " ", `[-_\s]*`)
						appVal = strings.ReplaceAll(appVal, "-", `[-_\s]*`)
						appVal = strings.ReplaceAll(appVal, "_", `[-_\s]*`)

						contextutils.AppValues[appName] = append(contextutils.AppValues[appName], appVal)
					}

					contextutils.DefaultAppValues = append(contextutils.DefaultAppValues, includedProperty)

				case property.TEAM_PROPERTY_NAME:

					var teamVals = strings.Split(includedProperty, ",")

					teamName := teamVals[0]

					for _, teamVal := range teamVals {

						teamVal = strings.ToLower(teamVal)
						if len(teamVal) <= 4 {
							teamVal = `\b` + teamVal + `\b`
						}
						teamVal = strings.ReplaceAll(teamVal, " ", `[-_\s]*`)
						teamVal = strings.ReplaceAll(teamVal, "-", `[-_\s]*`)
						teamVal = strings.ReplaceAll(teamVal, "_", `[-_\s]*`)

						contextutils.TeamValues[teamName] = append(contextutils.TeamValues[teamName], teamVal)
					}

					contextutils.DefaultTeamValues = append(contextutils.DefaultTeamValues, includedProperty)
				}
			}
		}
	}

	logger.Print(logger.INFO, "Global App Context for tenant", []string{resourceContext.TenantID}, contextutils.DefaultAppValues)
	logger.Print(logger.INFO, "Global Team Context for tenant", []string{resourceContext.TenantID}, contextutils.DefaultTeamValues)
}

func SetGlobalOrgContext(resourceContext *rcontext.ResourceContext) {

	logger.Print(logger.INFO, "Setting Global App Context for tenant", []string{resourceContext.TenantID}, contextutils.DefaultAppValues)
	logger.Print(logger.INFO, "Setting Global Team Context for tenant", []string{resourceContext.TenantID}, contextutils.DefaultTeamValues)

	docID := common.GenerateCombinedHashID(property.ORG_CONTEXT_TYPE, resourceContext.TenantID)

	orgContext, err := elastic.GetDocument(elastic.CUST_ENTITY_CONTEXT_INDEX, docID)
	if err != nil {
		return
	}

	if len(orgContext) > 0 {

		orgContextJson, err := json.Marshal(orgContext)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			return
		}

		var orgEntityContext common.CustomerEntityContextDoc

		if err = json.Unmarshal(orgContextJson, &orgEntityContext); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		if orgEntityContext.ContextProperties == nil {
			orgEntityContext.ContextProperties = []common.EntityContextProperties{
				{
					PropertyName: property.APP_PROPERTY_NAME,
					Include:      contextutils.DefaultAppValues,
				},
				{
					PropertyName: property.TEAM_PROPERTY_NAME,
					Include:      contextutils.DefaultTeamValues,
				},
			}
		} else {
			for i, contextProperty := range orgEntityContext.ContextProperties {

				switch contextProperty.PropertyName {
				case property.APP_PROPERTY_NAME:
					tmp := orgEntityContext.ContextProperties[i]
					tmp.Include = append(contextutils.DefaultAppValues, []string{}...)
					orgEntityContext.ContextProperties[i] = tmp
				case property.TEAM_PROPERTY_NAME:
					tmp := orgEntityContext.ContextProperties[i]
					tmp.Include = append(contextutils.DefaultTeamValues, []string{}...)
					orgEntityContext.ContextProperties[i] = tmp
				}
			}
		}

		currentTime, _ := elastic.ParseDateTime(resourceContext.CurrentTime)
		if len(orgEntityContext.InsertTime) <= 0 {
			orgEntityContext.InsertTime = elastic.DateTime(currentTime.UTC())
		}
		orgEntityContext.UpdateTime = elastic.DateTime(currentTime.UTC())

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			return
		}

	} else {

		currentTime, _ := elastic.ParseDateTime(resourceContext.CurrentTime)
		orgEntityContext := common.CustomerEntityContextDoc{
			InsertTime: elastic.DateTime(currentTime.UTC()),
			TenantID:   resourceContext.TenantID,
			UpdateTime: elastic.DateTime(currentTime.UTC()),
			ID:         docID,
			Type:       property.ORG_CONTEXT_TYPE,
			ContextProperties: []common.EntityContextProperties{
				{
					PropertyName: property.APP_PROPERTY_NAME,
					Include:      contextutils.DefaultAppValues,
				},
				{
					PropertyName: property.TEAM_PROPERTY_NAME,
					Include:      contextutils.DefaultTeamValues,
				},
			},
		}

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			return
		}

	}
}

func ProcessGlobalApps(resourceContext *rcontext.ResourceContext) {
	if len(contextutils.AIDetectedApps) == 0 {
		return
	}

	logger.Print(logger.INFO, fmt.Sprintf("Processing %d AI detected apps for tenant", len(contextutils.AIDetectedApps)), []string{resourceContext.TenantID})

	recipients := map[string]string{
		"Aniket": "<EMAIL>",
		"Abhay":  "<EMAIL>",
	}

	subject := fmt.Sprintf("AI Detected Apps for Tenant: %s in %s", resourceContext.TenantID, config.Environment)

	if len(contextutils.AIDetectedApps) <= 1000 {
		body := formatAIDetectedAppsEmailBody(resourceContext.TenantID, contextutils.AIDetectedApps, 0, len(contextutils.AIDetectedApps))
		body += "\n\n Note: If the detection is incorrect, please add the app in text_lookup with ignoreGlobalApp::: prefix"

		err := email.SendEmail(subject, body, recipients, nil, nil)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to send email with AI detected apps", []string{resourceContext.TenantID}, err)
		}
	} else {

		batchSize := 1000
		totalApps := len(contextutils.AIDetectedApps)
		batches := (totalApps + batchSize - 1) / batchSize

		appsList := make([]struct {
			Name    string
			Details string
		}, 0, totalApps)

		for app, details := range contextutils.AIDetectedApps {
			appsList = append(appsList, struct {
				Name    string
				Details string
			}{
				Name:    app,
				Details: details,
			})
		}

		for i := range batches {
			start := i * batchSize
			end := min(start+batchSize, totalApps)

			batchSubject := fmt.Sprintf("%s (Batch %d of %d)", subject, i+1, batches)
			batchBody := formatAIDetectedAppsEmailBodyFromSlice(resourceContext.TenantID, appsList[start:end])

			batchBody += "\n\n Note: If the detection is incorrect, please add the app in text_lookup with ignoreGlobalApp::: prefix"

			err := email.SendEmail(batchSubject, batchBody, recipients, nil, nil)
			if err != nil {
				logger.Print(logger.ERROR, fmt.Sprintf("Failed to send email batch %d of %d with AI detected apps", i+1, batches),
					[]string{resourceContext.TenantID}, err)
			} else {
				logger.Print(logger.INFO, fmt.Sprintf("Sent email batch %d of %d with AI detected apps", i+1, batches),
					[]string{resourceContext.TenantID})
			}
		}
	}

	logger.Print(logger.INFO, "Finished processing AI detected apps for tenant", []string{resourceContext.TenantID})
}

func formatAIDetectedAppsEmailBody(tenantID string, apps map[string]string, start, end int) string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("AI Detected Apps for Tenant: %s\n\n", tenantID))

	i := 0
	count := 0
	for app, details := range apps {
		if count >= start && count < end {
			builder.WriteString(fmt.Sprintf("%d. %s: %s\n", i+1, app, details))
			i++
		}
		count++
		if count >= end {
			break
		}
	}

	return builder.String()
}

func formatAIDetectedAppsEmailBodyFromSlice(tenantID string, apps []struct {
	Name    string
	Details string
}) string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("AI Detected Apps for Tenant: %s\n\n", tenantID))

	if len(apps) == 0 {
		builder.WriteString("No AI detected apps found.")
		return builder.String()
	}

	for i, app := range apps {
		builder.WriteString(fmt.Sprintf("%d. %s: %s\n", i+1, app.Name, app.Details))
	}

	return builder.String()
}

func AIRejectedGlobalContext(resourceContext *rcontext.ResourceContext) {
	if len(contextutils.AIRejectedTeamValues) == 0 {
		return
	}

	logger.Print(logger.INFO, "AI Rejected Global Team Context for tenant", []string{resourceContext.TenantID}, contextutils.AIRejectedTeamValues)

	if len(contextutils.AIRejectedAppValues) == 0 {
		return
	}

	logger.Print(logger.INFO, "AI Rejected Global App Context for tenant", []string{resourceContext.TenantID}, contextutils.AIRejectedAppValues)
}
