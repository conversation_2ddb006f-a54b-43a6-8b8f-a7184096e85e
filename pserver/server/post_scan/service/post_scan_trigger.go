package service

import (
	"encoding/json"
	"fmt"

	"github.com/precize/elastic"
)

func ProcessPostScanTrigger(req PostScanRequest) (map[string]any, error) {
	result := make(map[string]any)
	var triggeredServices []string
	var errors []string

	if req.ServiceID != "" {
		var serviceID int
		for id, name := range ServiceIDToName {
			if name == req.ServiceID {
				serviceID = id
				break
			}
		}

		collectedAt, err := getLatestCollectedAt(req.TenantID, serviceID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to get collectedAt for service %s: %v", req.ServiceID, err))
		} else if collectedAt == 0 {
			errors = append(errors, fmt.Sprintf("No scan data found for tenant %s and service %s", req.TenantID, req.ServiceID))
		} else {
			err := callPostScanAPI(req.ServiceID, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", req.ServiceID, err))
			} else {
				triggeredServices = append(triggeredServices, req.ServiceID)
			}
		}
	} else {
		serviceCollectedAtMap, err := getAllServicesCollectedAt(req.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get services data: %v", err)
		}

		if len(serviceCollectedAtMap) == 0 {
			return nil, fmt.Errorf("no scan data found for tenant %s", req.TenantID)
		}

		for serviceID, collectedAt := range serviceCollectedAtMap {
			serviceName, ok := ServiceIDToName[serviceID]
			if !ok {
				errors = append(errors, fmt.Sprintf("Unknown service ID: %d", serviceID))
				continue
			}

			err := callPostScanAPI(serviceName, req.TenantID, collectedAt, req.Environment, req.Params)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to trigger post scan for service %s: %v", serviceName, err))
			} else {
				triggeredServices = append(triggeredServices, serviceName)
			}
		}
	}

	result["triggeredServices"] = triggeredServices
	if len(errors) > 0 {
		result["errors"] = errors
	}

	return result, nil
}

func getLatestCollectedAt(tenantID string, serviceID int) (int64, error) {
	query := fmt.Sprintf(`{
        "size": 1,
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "tenantId.keyword": "%s"
                        }
                    },
                    {
                        "term": {
                            "serviceId": %d
                        }
                    },
                    {
                        "match": {
                            "status": "2"
                        }
                    },
                    {
                        "match": {
                            "scanType": "0"
                        }
                    }
                ]
            }
        },
        "sort": [
            {
                "collectedAt": {
                    "order": "desc"
                }
            }
        ],
        "_source": ["collectedAt", "id"]
    }`, tenantID, serviceID)

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{"scans"}, query)
	if err != nil {
		return 0, err
	}

	for _, doc := range docs {
		if collectedAtFloat, ok := doc["collectedAt"].(float64); ok {
			return int64(collectedAtFloat), nil
		}
	}

	return 0, nil
}

func getAllServicesCollectedAt(tenantID string) (map[int]int64, error) {
	query := fmt.Sprintf(`{
        "size": 0,
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "tenantId.keyword": "%s"
                        }
                    },
                    {
                        "match": {
                            "status": "2"
                        }
                    },
                    {
                        "match": {
                            "scanType": "0"
                        }
                    }
                ]
            }
        },
        "aggs": {
            "by_service": {
                "terms": {
                    "field": "serviceId",
                    "size": 1000
                },
                "aggs": {
                    "latest_collectedAt": {
                        "top_hits": {
                            "sort": [
                                {
                                    "collectedAt": {
                                        "order": "desc"
                                    }
                                }
                            ],
                            "size": 1,
                            "_source": {
                                "includes": [
                                    "collectedAt"
                                ]
                            }
                        }
                    }
                }
            }
        }
    }`, tenantID)

	eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{"scans"}, query)
	if err != nil {
		return nil, err
	}

	eventsAggBytes, err := json.Marshal(eventsAggregation)
	if err != nil {
		return nil, err
	}

	var result struct {
		ByService struct {
			Buckets []struct {
				Key               int `json:"key"`
				LatestCollectedAt struct {
					Hits struct {
						Hits []struct {
							Source struct {
								CollectedAt int64 `json:"collectedAt"`
							} `json:"_source"`
						} `json:"hits"`
					} `json:"hits"`
				} `json:"latest_collectedAt"`
			} `json:"buckets"`
		} `json:"by_service"`
	}

	if err := json.Unmarshal(eventsAggBytes, &result); err != nil {
		return nil, err
	}

	serviceCollectedAtMap := make(map[int]int64)
	for _, serviceBucket := range result.ByService.Buckets {
		if len(serviceBucket.LatestCollectedAt.Hits.Hits) > 0 {
			serviceCollectedAtMap[serviceBucket.Key] = serviceBucket.LatestCollectedAt.Hits.Hits[0].Source.CollectedAt
		}
	}

	return serviceCollectedAtMap, nil
}
